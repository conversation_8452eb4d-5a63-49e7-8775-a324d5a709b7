<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>多图片上传与识别</title>
    <!-- 添加Word导出所需的库 -->
    <script src="utils/pizzip.min.js"></script>
    <script src="utils/docxtemplater.js"></script>
    <script src="utils/FileSaver.min.js"></script>
    <script src="utils/imagemodule.js"></script>
    <link rel="stylesheet" href="style.css">
</head>

<body>
    <div class="main-container">
        <div class="header">
            <h1>变压器绕组测试报告生成系统</h1>
        </div>

        <form id="info-form">
            <div class="section">
                <div class="section-title">基本信息与试验环境</div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="company_name">变电站名称</label>
                        <input type="text" name="company_name" id="company_name" class="form-control"
                            placeholder="请输入变电站名称" required>
                    </div>
                    <div class="form-group">
                        <label for="operation_number">设备名称</label>
                        <input type="text" name="operation_number" id="operation_number" class="form-control"
                            placeholder="请输入设备名称" required>
                    </div>
                    <div class="form-group">
                        <label for="device_model">设备型号</label>
                        <input type="text" name="device_model" id="device_model" class="form-control"
                            placeholder="请输入设备型号">
                    </div>
                    <div class="form-group">
                        <label for="manufacturer">制造厂家</label>
                        <input type="text" name="manufacturer" id="manufacturer" class="form-control"
                            placeholder="请输入制造厂家">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="serial_number">出厂编号</label>
                        <input type="text" name="serial_number" id="serial_number" class="form-control"
                            placeholder="请输入出厂编号">
                    </div>
                    <div class="form-group">
                        <label for="production_date">出厂日期</label>
                        <input type="date" name="production_date" id="production_date" class="form-control">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="test_date">试验日期</label>
                        <input type="date" name="test_date" id="test_date" class="form-control">
                    </div>
                    <div class="form-group">
                        <label for="test_nature">试验性质</label>
                        <input type="text" name="test_nature" id="test_nature" class="form-control"
                            placeholder="例: 交接、预试">
                    </div>
                    <div class="form-group">
                        <label for="weather">天气</label>
                        <input type="text" name="weather" id="weather" class="form-control" placeholder="例: 晴、阴、小雨">
                    </div>
                    <div class="form-group">
                        <label for="temperature">温度</label>
                        <input type="number" name="temperature" id="temperature" class="form-control" step="any"
                            placeholder="单位: ℃">
                    </div>
                    <div class="form-group">
                        <label for="humidity">湿度</label>
                        <input type="number" name="humidity" id="humidity" class="form-control" step="any"
                            placeholder="单位: %">
                    </div>
                    <div class="form-group">
                        <label for="test_leader">试验负责人</label>
                        <input type="text" name="test_leader" id="test_leader" class="form-control"
                            placeholder="请输入负责人姓名">
                    </div>
                    <div class="form-group">
                        <label for="test_people">试验人员</label>
                        <input type="text" name="test_people" id="test_people" class="form-control"
                            placeholder="请输入试验人员姓名">
                    </div>
                </div>
                <div class="form-row">
                    <!-- 暂时屏蔽图片上传控件 -->
                    <!-- <div class="form-group">
                            <label for="nameplate_img">铭牌照片</label>
                            <input type="file" name="nameplate_img" id="nameplate_img" class="form-control"
                                accept="image/*">
                        </div> -->
                </div>
                <div style="margin-top:24px;">
                    <div class="section-title" style="font-size: 18px; margin-bottom: 12px;">铭牌阻抗输入</div>
                    <table class="result-table">
                        <thead>
                            <tr>
                                <th>绕组对</th>
                                <th>最大正</th>
                                <th>额定</th>
                                <th>最大负</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <th>高对中</th>
                                <td><input type="number" name="np_gy_zy_max" placeholder="数值"></td>
                                <td><input type="number" name="np_gy_zy_rated" placeholder="数值"></td>
                                <td><input type="number" name="np_gy_zy_min" placeholder="数值"></td>
                            </tr>
                            <tr>
                                <th>高对低</th>
                                <td><input type="number" name="np_gy_dy_max" placeholder="数值"></td>
                                <td><input type="number" name="np_gy_dy_rated" placeholder="数值"></td>
                                <td><input type="number" name="np_gy_dy_min" placeholder="数值"></td>
                            </tr>
                            <tr>
                                <th>中对低</th>
                                <td></td>
                                <td><input type="number" name="np_zy_dy_rated" placeholder="数值"></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>
                    <!-- 电压信息输入 -->
                    <div style="margin-top:24px;">
                        <div class="section-title" style="font-size: 18px; margin-bottom: 12px;">电压等级信息</div>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="voltage_high">高压侧电压</label>
                                <select name="voltage_high" id="voltage_high" class="form-control"
                                    onchange="updateVoltages()">
                                    <option value="500">500kV</option>
                                    <option value="220">220kV</option>
                                    <option value="110">110kV</option>
                                    <option value="35">35kV</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="voltage_medium">中压侧电压</label>
                                <select name="voltage_medium" id="voltage_medium" class="form-control">
                                    <option value="220">220kV</option>
                                    <option value="110">110kV</option>
                                    <option value="35">35kV</option>
                                    <option value="none">/</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="voltage_low">低压侧电压</label>
                                <select name="voltage_low" id="voltage_low" class="form-control">
                                    <option value="35">35kV</option>
                                    <option value="10">10kV</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 档位信息输入 -->
                    <div style="margin-top:24px;">
                        <div class="section-title" style="font-size: 18px; margin-bottom: 12px;">分接开关档位信息</div>
                        <div class="form-row">
                            <!-- 高压侧档位 -->
                            <div class="form-group" style="grid-column: 1 / 3;">
                                <label>高压侧档位设置</label>
                                <div class="input-group">
                                    <div class="input-row" style="display:flex;gap:12px;margin-bottom:8px;">
                                        <div style="flex:1;">
                                            <span>最正档位:</span>
                                            <input type="number" name="voltage_high_max" id="voltage_high_max"
                                                class="form-control" min="1" value="1">
                                        </div>
                                        <div style="flex:1;">
                                            <span>额定档位:</span>
                                            <input type="number" name="voltage_high_rated" id="voltage_high_rated"
                                                class="form-control" min="1" value="9">
                                        </div>
                                        <div style="flex:1;">
                                            <span>最负档位:</span>
                                            <input type="number" name="voltage_high_min" id="voltage_high_min"
                                                class="form-control" min="1" value="17">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 中压侧档位 -->
                            <div class="form-group">
                                <label>中压侧档位设置</label>
                                <div class="input-group">
                                    <div class="input-row" style="display:flex;gap:12px;">
                                        <div style="flex:1;">
                                            <span>额定档位:</span>
                                            <input type="number" name="voltage_medium_rated" id="voltage_medium_rated"
                                                class="form-control" min="1" value="3">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="section-title" style="font-size: 18px; margin-bottom: 12px;">选择试验结论</div>
                <div class="form-group">
                    <label for="conclusion">试验结论</label>
                    <select name="conclusion" id="conclusion" class="form-control">
                        <option value="本次试验合格">合格</option>
                        <option value="本次试验不合格">不合格</option>
                    </select>
                </div>

            </div>
        </form>

        <div class="section">
            <div class="section-title">线圈测试数据（上传并框选图片）</div>
            <div class="tab-bar">
                <div class="tab-indicator" id="tab-indicator"></div>
                <button type="button" class="tab-btn active" id="tab-single">
                    <i class="icon">📊</i> 单相法
                </button>
                <button type="button" class="tab-btn" id="tab-inter">
                    <i class="icon">🔄</i> 相间法
                </button>
            </div>

            <div id="tab-content-single">
                <div class="img-group-title">高压-中压</div>
                <div class="img-group" id="img-group-gyzy"></div>

                <div class="img-group-title">高压-低压</div>
                <div class="img-group" id="img-group-gydy"></div>

                <div class="img-group-title">中压-低压</div>
                <div class="img-group" id="img-group-zydy"></div>
            </div>

            <div id="tab-content-inter" style="display:none;">
                <div class="img-group-title">高压-中压</div>
                <div class="img-group" id="img-group-gyzy-inter"></div>

                <div class="img-group-title">高压-低压</div>
                <div class="img-group" id="img-group-gydy-inter"></div>

                <div class="img-group-title">中压-低压</div>
                <div class="img-group" id="img-group-zydy-inter"></div>
            </div>
        </div>
        <div class="section">
            <div class="section-title">生成报告并导出</div>
            <div class="upload-container">
                <div class="img-group-title" style="margin:0 0 10px 0;">请选择模版</div>

                <div class="upload-area" id="dropZone">
                    <div class="upload-icon">📄</div>
                    <div class="upload-text">拖放文件到此处或点击选择</div>
                    <div class="upload-hint">支持 .docx 格式文档</div>
                </div>

                <input type="file" id="custom-template" class="file-input" accept=".docx" required />

                <button class="upload-btn" id="uploadTrigger">
                    <span class="btn-icon">📁</span> 选择Word模板
                </button>

                <div class="file-preview" id="filePreview">
                    <div class="preview-header">
                        <div class="file-name">
                            <span class="file-icon">📄</span>
                            <span id="fileName">文件名.docx</span>
                        </div>
                        <button class="file-remove" id="removeFile">✕</button>
                    </div>

                    <div class="file-info">
                        <div class="file-type" id="fileType">Word文档</div>
                        <div class="file-size" id="fileSize">0 KB</div>
                    </div>

                    <div class="file-info">
                        <div class="file-status status-pending" id="fileStatus">等待上传</div>
                    </div>
                </div>
                <button class="export-btn" id="btn-export">
                    <span class="btn-icon">📥</span> 数据导出
                </button>
            </div>

        </div>

        <!-- 隐藏的模板文件选择器 -->
        <input type="file" id="template-file" accept=".docx" style="display:none">
        <script src="utils\index.umd.js"></script>
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const dropZone = document.getElementById('dropZone');
                const fileInput = document.getElementById('custom-template');
                const uploadTrigger = document.getElementById('uploadTrigger');
                const filePreview = document.getElementById('filePreview');
                const fileName = document.getElementById('fileName');
                const fileSize = document.getElementById('fileSize');
                const fileType = document.getElementById('fileType');
                const fileStatus = document.getElementById('fileStatus');
                const removeFile = document.getElementById('removeFile');

                // 点击按钮触发文件选择
                uploadTrigger.addEventListener('click', function () {
                    fileInput.click();
                });

                // 点击上传区域触发文件选择
                dropZone.addEventListener('click', function () {
                    fileInput.click();
                });

                // 监听文件选择变化
                fileInput.addEventListener('change', function (e) {
                    if (this.files.length > 0) {
                        handleFile(this.files[0]);
                    }
                });

                // 拖放功能
                dropZone.addEventListener('dragover', function (e) {
                    e.preventDefault();
                    this.classList.add('dragover');
                });

                dropZone.addEventListener('dragleave', function () {
                    this.classList.remove('dragover');
                });

                dropZone.addEventListener('drop', function (e) {
                    e.preventDefault();
                    this.classList.remove('dragover');

                    if (e.dataTransfer.files.length > 0) {
                        handleFile(e.dataTransfer.files[0]);
                        fileInput.files = e.dataTransfer.files;
                    }
                });

                // 移除文件
                removeFile.addEventListener('click', function (e) {
                    e.stopPropagation();
                    fileInput.value = '';
                    filePreview.style.display = 'none';
                });

                // 处理选择的文件
                function handleFile(file) {
                    // 验证文件类型
                    if (!file.name.endsWith('.docx')) {
                        fileStatus.textContent = '文件格式错误';
                        fileStatus.className = 'file-status status-pending';
                        fileStatus.style.backgroundColor = '#fff4e6';
                        fileStatus.style.color = '#FF9500';
                        return;
                    }

                    // 显示文件信息
                    fileName.textContent = file.name;
                    fileSize.textContent = formatFileSize(file.size);
                    fileType.textContent = 'Word文档';

                    // 显示预览区域
                    filePreview.style.display = 'block';

                    // 模拟上传过程
                    fileStatus.textContent = '上传中...';
                    fileStatus.className = 'file-status status-pending';

                    setTimeout(function () {
                        fileStatus.textContent = '上传成功';
                        fileStatus.className = 'file-status status-success';
                    }, 1500);
                }

                // 格式化文件大小
                function formatFileSize(bytes) {
                    if (bytes === 0) return '0 Bytes';
                    const k = 1024;
                    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                }
            });
            // 图片状态管理
            const imgStates = {};

            // 图片组配置
            const groups = [
                {
                    title: "高压-中压",
                    keys: [
                        {
                            key: 'gy_zy_max', label: '高压-中压 最大正', dataMap: {
                                0: 'zkzz', 1: 'xkzz', 2: 'ukzz', 4: 'lkzz'
                            }
                        },
                        {
                            key: 'gy_zy_rated', label: '高压-中压 额定', dataMap: {
                                0: 'zked', 1: 'xked', 2: 'uked', 4: 'lked'
                            }
                        },
                        {
                            key: 'gy_zy_min', label: '高压-中压 最大负', dataMap: {
                                0: 'zkzf', 1: 'xkzf', 2: 'ukzf', 4: 'lkzf'
                            }
                        }
                    ],
                    container: 'img-group-gyzy'
                },
                {
                    title: "高压-低压",
                    keys: [
                        { key: 'gy_dy_max', label: '高压-低压 最大正' },
                        { key: 'gy_dy_rated', label: '高压-低压 额定' },
                        { key: 'gy_dy_min', label: '高压-低压 最大负' }
                    ],
                    container: 'img-group-gydy'
                },
                {
                    title: "中压-低压",
                    keys: [
                        { key: 'zy_dy_rated', label: '中压-低压 额定' }
                    ],
                    container: 'img-group-zydy'
                }
            ];

            // 渲染图片组
            function renderImgGroups() {
                // 渲染单相法图片组
                renderSinglePhaseGroups();

                // 渲染相间法图片组
                renderInterPhaseGroups();

                // 确保 showCropModal 函数在全局作用域中可用
                ensureCropModalFunction();
            }

            // 渲染单相法图片组
            function renderSinglePhaseGroups() {
                groups.forEach(group => {
                    const imgGroup = document.getElementById(group.container);
                    if (!imgGroup) return;

                    imgGroup.innerHTML = '';
                    group.keys.forEach(item => {
                        initializeImageState(item.key);
                        const block = createImageBlock(item, false);
                        imgGroup.appendChild(block);
                        bindImageBlockEvents(block, item.key, item);
                    });
                });
            }

            // 渲染相间法图片组
            function renderInterPhaseGroups() {
                groups.forEach(group => {
                    const imgGroup = document.getElementById(group.container + '-inter');
                    if (!imgGroup) return;

                    imgGroup.innerHTML = '';
                    group.keys.forEach(item => {
                        const interKey = 'inter_' + item.key;
                        initializeImageState(interKey);
                        const block = createImageBlock(item, true);
                        imgGroup.appendChild(block);
                        bindImageBlockEvents(block, interKey, item, true);
                    });
                });
            }

            // 初始化图片状态
            function initializeImageState(key) {
                if (!imgStates[key]) {
                    imgStates[key] = {
                        loaded: false,
                        image: null,
                        displayScale: 1,
                        result: null,
                        quad: null, // 框选区域
                        ocrTable: null,
                        ocrText: null,
                        step: 0 // 0:未选图 1:已选图 2:已框选 3:已识别
                    };
                }
            }

            // 创建图片块DOM元素
            function createImageBlock(item, isInterPhase = false) {
                const key = isInterPhase ? 'inter_' + item.key : item.key;
                const block = document.createElement('div');
                block.className = 'img-block';

                const impedanceInput = isInterPhase ? `
                    <div style="margin:10px 0 0 0;text-align:center;">
                        <label style="font-size:15px;color:#0068e0;font-weight:600;margin-right:8px;">实测阻抗(%)</label>
                        <input type="number" step="any" id="zk_input_${key}" style="width:120px;font-size:16px;padding:6px 10px;border-radius:6px;border:1px solid #e0e0e0;">
                    </div>
                ` : '';

                block.innerHTML = `
                    <div class="img-block-title">${item.label}<span class="status-indicator" id="status_${key}"></span></div>
                    <div class="img-flow-steps" style="display:flex;gap:18px;justify-content:center;margin:18px 0 12px 0;">
                        <button type="button" id="btn_select_${key}" class="flow-btn">
                            <i class="icon">🖼️</i> 选择图片
                            <span id="step1_${key}" class="step-check"></span>
                        </button>
                        <button type="button" id="btn_crop_${key}" class="flow-btn deactive" disabled>
                            <i class="icon">🔍</i> 框选区域
                            <span id="step2_${key}" class="step-check"></span>
                        </button>
                        <button type="button" id="btn_result_${key}" class="flow-btn deactive" disabled>
                            <i class="icon">📋</i> 查看结果
                            <span id="step3_${key}" class="step-check"></span>
                        </button>
                    </div>
                    <input type="file" accept="image/*" id="file_${key}" style="display:none;">
                    <canvas class="img-canvas" id="canvas_${key}" width="400" height="300" style="display:none;margin-bottom:10px;"></canvas>
                    ${impedanceInput}
                `;

                return block;
            }

            // 绑定图片块事件
            function bindImageBlockEvents(block, key, item, isInterPhase = false) {
                const fileInput = block.querySelector(`#file_${key}`);
                const btnSelect = block.querySelector(`#btn_select_${key}`);
                const btnCrop = block.querySelector(`#btn_crop_${key}`);
                const btnResult = block.querySelector(`#btn_result_${key}`);
                const canvas = block.querySelector(`#canvas_${key}`);
                const ctx = canvas.getContext('2d');

                // 绑定选择图片事件
                bindSelectImageEvents(fileInput, btnSelect, btnCrop, btnResult, canvas, ctx, key, isInterPhase);

                // 绑定框选区域事件
                bindCropEvents(btnCrop, key);

                // 绑定查看结果事件
                bindResultEvents(btnResult, key, isInterPhase);
            }

            // 绑定选择图片相关事件
            function bindSelectImageEvents(fileInput, btnSelect, btnCrop, btnResult, canvas, ctx, key, isInterPhase) {
                btnSelect.onclick = function () {
                    fileInput.value = '';
                    fileInput.click();
                };

                fileInput.onchange = function (e) {
                    const file = e.target.files[0];
                    if (file) {
                        handleImageSelection(file, canvas, ctx, key, btnSelect, btnCrop, btnResult, isInterPhase);
                    }
                };
            }

            // 处理图片选择
            function handleImageSelection(file, canvas, ctx, key, btnSelect, btnCrop, btnResult, isInterPhase) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    const image = new Image();
                    image.src = e.target.result;
                    image.onload = function () {
                        // 更新图片状态
                        updateImageState(key, image, canvas);

                        // 绘制图片到画布
                        drawImageToCanvas(image, canvas, ctx, key);

                        // 更新按钮状态
                        updateButtonStates(btnSelect, btnCrop, btnResult, isInterPhase);
                    };
                };
                reader.readAsDataURL(file);
            }

            // 更新图片状态
            function updateImageState(key, image, canvas) {
                imgStates[key].image = image;
                imgStates[key].displayScale = Math.min(canvas.width / image.width, canvas.height / image.height);
                imgStates[key].rect = null;
                imgStates[key].ocrTable = null;
                imgStates[key].ocrText = null;
                imgStates[key].step = 1;
            }

            // 绘制图片到画布
            function drawImageToCanvas(image, canvas, ctx, key) {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                const scale = imgStates[key].displayScale;
                const imgW = image.width * scale;
                const imgH = image.height * scale;
                const offsetX = (canvas.width - imgW) / 2;
                const offsetY = (canvas.height - imgH) / 2;
                ctx.drawImage(image, offsetX, offsetY, imgW, imgH);
                canvas.style.display = 'block';
            }

            // 更新按钮状态
            function updateButtonStates(btnSelect, btnCrop, btnResult, isInterPhase) {
                btnSelect.classList.add('completed');
                btnCrop.classList.remove('completed');
                btnResult.classList.remove('completed');

                btnCrop.disabled = false;
                btnCrop.classList.remove('deactive');
                btnResult.disabled = true;
                btnResult.classList.add('deactive');

                if (isInterPhase) {
                    updateFlowButtons('img-group-gyzy-inter');
                }
            }

            // 绑定框选区域事件
            function bindCropEvents(btnCrop, key) {
                btnCrop.onclick = function () {
                    if (!imgStates[key].image) return;
                    if (btnCrop.disabled) return;
                    window.showCropModal(key);
                };
            }

            // 绑定查看结果事件
            function bindResultEvents(btnResult, key, isInterPhase) {
                btnResult.onclick = function () {
                    window.dispatchEvent(new CustomEvent('show-ocr-result', { detail: { key: key } }));
                    const step3 = document.querySelector(`#step3_${key}`);
                    if (step3) step3.innerHTML = '';
                    btnResult.classList.add('completed');
                };
            }

            // 确保 showCropModal 函数存在
            function ensureCropModalFunction() {
                if (typeof window.showCropModal !== 'function') {
                    window.showCropModal = createCropModalFunction();
                }
            }

            // 创建框选模态框函数
            function createCropModalFunction() {
                return function (key) {
                    const modal = getOrCreateCropModal();
                    showCropModal(modal, key);
                };
            }

            // 获取或创建框选模态框
            function getOrCreateCropModal() {
                let modal = document.getElementById('crop-modal');
                if (!modal) {
                    modal = createCropModalElement();
                    document.body.appendChild(modal);
                    bindCropModalCloseEvent(modal);
                }
                return modal;
            }

            // 创建框选模态框元素
            function createCropModalElement() {
                const modal = document.createElement('div');
                modal.id = 'crop-modal';
                modal.className = 'modal-overlay';
                modal.style.display = 'none';
                modal.innerHTML = `
                    <div class="modal-content">
                        <div class="modal-header">
                            <h3 class="modal-title">框选识别区域</h3>
                            <button class="modal-close" id="crop-modal-close">×</button>
                        </div>
                        <div class="info-box">
                            <p>• 请点击图片四个位置创建四边形顶点</p>
                            <p>• 拖拽点可以调整位置</p>
                            <p>• 连线会自动创建</p>
                        </div>
                        <div class="canvas-container">
                            <canvas id="crop-modal-canvas" class="crop-canvas"></canvas>
                            <div id="point-markers" class="point-markers"></div>
                        </div>
                        <div class="status-message" id="crop-status" style="display: none; margin-top: 16px; padding: 10px; background: #fff4e6; border-radius: 8px; color: #ff9500;"></div>
                        <div class="modal-actions">
                            <button class="modal-btn btn-reset" id="reset-selection">重置</button>
                            <button class="modal-btn btn-ok" id="confirm-selection">确定</button>
                        </div>
                    </div>
                `;
                return modal;
            }

            // 绑定框选模态框关闭事件
            function bindCropModalCloseEvent(modal) {
                document.getElementById('crop-modal-close').onclick = function () {
                    modal.style.display = 'none';
                };
            }

            // 显示框选模态框
            function showCropModal(modal, key) {
                modal.style.display = 'flex';

                const canvas = document.getElementById('crop-modal-canvas');
                const ctx = canvas.getContext('2d');
                const pointMarkers = document.getElementById('point-markers');
                const confirmBtn = document.getElementById('confirm-selection');
                const resetBtn = document.getElementById('reset-selection');
                const statusMessage = document.getElementById('crop-status');

                // 清空点标记
                pointMarkers.innerHTML = '';

                // 获取图片状态
                const state = imgStates[key];
                if (!state || !state.image) return;

                // 设置画布和绘制图片
                setupCropCanvas(canvas, ctx, state);

                // 初始化点选择功能
                const points = [];
                initializePointSelection(canvas, pointMarkers, points, statusMessage);

                // 绑定确定和重置按钮事件
                bindCropModalButtons(confirmBtn, resetBtn, points, pointMarkers, statusMessage, key, canvas, state);
            }

            // 设置框选画布
            function setupCropCanvas(canvas, ctx, state) {
                const maxWidth = Math.min(800, window.innerWidth * 0.8);
                const maxHeight = Math.min(600, window.innerHeight * 0.7);
                const scale = Math.min(maxWidth / state.image.width, maxHeight / state.image.height, 1);

                canvas.width = state.image.width * scale;
                canvas.height = state.image.height * scale;

                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(state.image, 0, 0, canvas.width, canvas.height);
            }

            // 初始化点选择功能
            function initializePointSelection(canvas, pointMarkers, points, statusMessage) {
                canvas.addEventListener('click', function (e) {
                    if (points.length >= 4) return;

                    const rect = canvas.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const marker = createPointMarker(x, y, points.length);
                    pointMarkers.appendChild(marker);
                    points.push({ x, y });

                    makeMarkerDraggable(marker, points.length - 1, points, pointMarkers);
                    drawQuadrilateral(points, pointMarkers);
                    updatePointStatus(points, statusMessage, pointMarkers);
                });
            }

            // 创建点标记
            function createPointMarker(x, y, index) {
                const marker = document.createElement('div');
                marker.className = 'point-marker';
                marker.style.position = 'absolute';
                marker.style.width = '16px';
                marker.style.height = '16px';
                marker.style.borderRadius = '50%';
                marker.style.backgroundColor = '#FF3B30';
                marker.style.border = '2px solid white';
                marker.style.transform = 'translate(-50%, -50%)';
                marker.style.cursor = 'move';
                marker.style.zIndex = '20';
                marker.style.left = `${x}px`;
                marker.style.top = `${y}px`;
                marker.dataset.index = index;
                return marker;
            }

            // 绘制四边形连线
            function drawQuadrilateral(points, pointMarkers) {
                document.querySelectorAll('.point-line').forEach(el => el.remove());

                for (let i = 0; i < points.length; i++) {
                    const next = (i + 1) % points.length;
                    if (points[i] && points[next]) {
                        const line = createConnectionLine(points[i], points[next]);
                        pointMarkers.appendChild(line);
                    }
                }
            }

            // 创建连接线
            function createConnectionLine(point1, point2) {
                const line = document.createElement('div');
                line.className = 'point-line';
                line.style.position = 'absolute';

                const dx = point2.x - point1.x;
                const dy = point2.y - point1.y;
                const length = Math.sqrt(dx * dx + dy * dy);
                const angle = Math.atan2(dy, dx) * 180 / Math.PI;

                line.style.width = `${length}px`;
                line.style.height = '2px';
                line.style.backgroundColor = '#0068e0';
                line.style.left = `${point1.x}px`;
                line.style.top = `${point1.y}px`;
                line.style.transform = `rotate(${angle}deg)`;
                line.style.transformOrigin = '0 0';
                line.style.zIndex = '10';

                return line;
            }

            // 使标记可拖拽
            function makeMarkerDraggable(marker, index, points, pointMarkers) {
                marker.addEventListener('mousedown', function(e) {
                    e.preventDefault();
                    const startX = e.clientX;
                    const startY = e.clientY;
                    const startLeft = parseFloat(marker.style.left);
                    const startTop = parseFloat(marker.style.top);

                    function doDrag(e) {
                        const currentX = e.clientX;
                        const currentY = e.clientY;
                        const newLeft = startLeft + (currentX - startX);
                        const newTop = startTop + (currentY - startY);

                        marker.style.left = `${newLeft}px`;
                        marker.style.top = `${newTop}px`;
                        points[index] = { x: newLeft, y: newTop };
                        drawQuadrilateral(points, pointMarkers);
                    }

                    function stopDrag() {
                        document.removeEventListener('mousemove', doDrag);
                        document.removeEventListener('mouseup', stopDrag);
                    }

                    document.addEventListener('mousemove', doDrag);
                    document.addEventListener('mouseup', stopDrag);
                });
            }

            // 更新点状态显示
            function updatePointStatus(points, statusMessage, pointMarkers) {
                statusMessage.textContent = `已设置点 ${points.length}/4`;
                statusMessage.style.display = 'block';

                if (points.length === 4) {
                    statusMessage.textContent = '四边形已完成，可拖拽调整顶点位置';
                    pointMarkers.classList.add('quad-complete');
                }
            }

            // 绑定框选模态框按钮事件
            function bindCropModalButtons(confirmBtn, resetBtn, points, pointMarkers, statusMessage, key, canvas, state) {
                confirmBtn.onclick = function () {
                    if (points.length < 4) {
                        statusMessage.textContent = '请设置四个顶点以完成四边形';
                        statusMessage.style.display = 'block';
                        return;
                    }

                    const quad = calculateQuadCoordinates(points, canvas, state);
                    imgStates[key].quad = quad;

                    document.getElementById('crop-modal').style.display = 'none';

                    setTimeout(() => {
                        const mainCanvas = document.getElementById(`canvas_${key}`);
                        const mainCtx = mainCanvas.getContext('2d');
                        drawQuadrilateralOnCanvas(mainCtx, mainCanvas, quad);
                        recognizeAndFill(key, mainCtx, mainCanvas);
                    }, 100);
                };

                resetBtn.onclick = function () {
                    points.length = 0;
                    pointMarkers.innerHTML = '';
                    statusMessage.style.display = 'none';
                    pointMarkers.classList.remove('quad-complete');
                };
            }

            // 计算四边形坐标
            function calculateQuadCoordinates(points, canvas, state) {
                return points.map(point => {
                    const rect = canvas.getBoundingClientRect();
                    const scaleX = state.image.width / canvas.width;
                    const scaleY = state.image.height / canvas.height;

                    return {
                        x: point.x * scaleX,
                        y: point.y * scaleY
                    };
                });
            }

            // 相间法：与单相法结构一致，容器id加 -inter
            groups.forEach(group => {
                const imgGroup = document.getElementById(group.container + '-inter');
                if (!imgGroup) return;
                imgGroup.innerHTML = '';
                group.keys.forEach(item => {
                    const interKey = 'inter_' + item.key;
                    if (!imgStates[interKey]) {
                        imgStates[interKey] = {
                            loaded: false,
                            image: null,
                            displayScale: 1,
                            result: null,
                            quad: null,
                            ocrTable: null,
                            ocrText: null,
                            step: 0
                        };
                    }
                    const block = document.createElement('div');
                    block.className = 'img-block';
                    // 增加实测阻抗输入框
                    block.innerHTML = `
                    <div class="img-block-title">${item.label}<span class="status-indicator" id="status_${interKey}"></span></div>
                    <div class="img-flow-steps" style="display:flex;gap:18px;justify-content:center;margin:18px 0 12px 0;">
                        <button type="button" id="btn_select_${interKey}" class="flow-btn">
                            <i class="icon">🖼️</i> 选择图片 
                            <span id="step1_${interKey}" class="step-check"></span>
                        </button>
                        <button type="button" id="btn_crop_${interKey}" class="flow-btn deactive" disabled>
                            <i class="icon">🔍</i> 框选区域 
                            <span id="step2_${interKey}" class="step-check"></span>
                        </button>
                        <button type="button" id="btn_result_${interKey}" class="flow-btn deactive" disabled>
                            <i class="icon">📋</i> 查看结果 
                            <span id="step3_${interKey}" class="step-check"></span>
                        </button>
                    </div>
                    <input type="file" accept="image/*" id="file_${interKey}" style="display:none;">
                    <canvas class="img-canvas" id="canvas_${interKey}" width="400" height="300" style="display:none;margin-bottom:10px;"></canvas>
                    <div style="margin:10px 0 0 0;text-align:center;">
                        <label style="font-size:15px;color:#0068e0;font-weight:600;margin-right:8px;">实测阻抗(%)</label>
                        <input type="number" step="any" id="zk_input_${interKey}" style="width:120px;font-size:16px;padding:6px 10px;border-radius:6px;border:1px solid #e0e0e0;">
                    </div>
                `;
                    imgGroup.appendChild(block);
                    // 选择图片
                    const fileInput = block.querySelector(`#file_${interKey}`);
                    const btnSelect = block.querySelector(`#btn_select_${interKey}`);
                    const btnCrop = block.querySelector(`#btn_crop_${interKey}`);
                    const btnResult = block.querySelector(`#btn_result_${interKey}`);
                    const step1 = block.querySelector(`#step1_${interKey}`);
                    const step2 = block.querySelector(`#step2_${interKey}`);
                    const step3 = block.querySelector(`#step3_${interKey}`);
                    const canvas = block.querySelector(`#canvas_${interKey}`);
                    const ctx = canvas.getContext('2d');
                    // 选择图片按钮
                    btnSelect.onclick = function () {
                        fileInput.value = '';
                        fileInput.click();
                    };
                    fileInput.onchange = function (e) {
                        const file = e.target.files[0];
                        if (file) {
                            const reader = new FileReader();
                            reader.onload = function (e) {
                                const image = new Image();
                                image.src = e.target.result;
                                image.onload = function () {
                                    imgStates[interKey].image = image;
                                    imgStates[interKey].displayScale = Math.min(canvas.width / image.width, canvas.height / image.height);
                                    imgStates[interKey].rect = null;
                                    imgStates[interKey].ocrTable = null;
                                    imgStates[interKey].ocrText = null;
                                    imgStates[interKey].step = 1;

                                    // 绘制图片
                                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                                    let scale = imgStates[interKey].displayScale;
                                    let imgW = image.width * scale;
                                    let imgH = image.height * scale;
                                    let offsetX = (canvas.width - imgW) / 2;
                                    let offsetY = (canvas.height - imgH) / 2;
                                    ctx.drawImage(image, offsetX, offsetY, imgW, imgH);
                                    canvas.style.display = 'block';

                                    // 更新按钮状态
                                    btnSelect.classList.add('completed');
                                    btnCrop.disabled = false;
                                    btnCrop.classList.remove('deactive');
                                    updateFlowButtons('img-group-gyzy-inter');
                                };
                            };
                            reader.readAsDataURL(file);
                        }
                    };                // 相间法下框选识别区域按钮
                    btnCrop.onclick = function () {
                        if (!imgStates[interKey].image) return;
                        if (btnCrop.disabled) return;
                        window.showCropModal(interKey);
                    }; btnResult.onclick = function () {
                        window.dispatchEvent(new CustomEvent('show-ocr-result', { detail: { key: interKey } }));
                        step3.innerHTML = '';
                        btnResult.classList.add('completed');
                    };
                });
            });

            // 自动识别并填充表格
            async function recognizeAndFill(key, ctx, mainCanvas) {
                // 识别中状态
                const statusIndicator = document.getElementById(`status_${key}`);
                statusIndicator.className = 'status-indicator yellow';
                let statusText = document.getElementById(`status_text_${key}`);
                if (!statusText) {
                    statusText = document.createElement('span');
                    statusText.id = `status_text_${key}`;
                    statusText.style = 'margin-left:8px;font-size:16px;font-weight:600;';
                    statusIndicator.parentNode.appendChild(statusText);
                }
                statusText.textContent = '正在识别...';
                statusText.style.color = '#ffcc00';

                // 使用 imgState 替代 state
                const imgState = imgStates[key];

                // 检查是否存在四边形区域
                if (!imgState.quad || imgState.quad.length !== 4) {
                    console.error('四边形区域数据无效');
                    statusIndicator.className = 'status-indicator red';
                    statusText.textContent = '识别失败：区域无效';
                    statusText.style.color = '#ff3b30';
                    return;
                }

                try {
                    // 创建裁剪画布
                    const cropCanvas = document.createElement('canvas');
                    const cropCtx = cropCanvas.getContext('2d');

                    // 计算四边形的最小包围盒
                    const minX = Math.min(...imgState.quad.map(p => p.x));
                    const minY = Math.min(...imgState.quad.map(p => p.y));
                    const maxX = Math.max(...imgState.quad.map(p => p.x));
                    const maxY = Math.max(...imgState.quad.map(p => p.y));

                    cropCanvas.width = maxX - minX;
                    cropCanvas.height = maxY - minY;

                    // 绘制四边形区域
                    cropCtx.save();
                    cropCtx.beginPath();
                    cropCtx.moveTo(imgState.quad[0].x - minX, imgState.quad[0].y - minY);
                    for (let i = 1; i < 4; i++) {
                        cropCtx.lineTo(imgState.quad[i].x - minX, imgState.quad[i].y - minY);
                    }
                    cropCtx.closePath();
                    cropCtx.clip();

                    // 绘制原始图像
                    cropCtx.drawImage(
                        imgState.image,
                        minX, minY, cropCanvas.width, cropCanvas.height,
                        0, 0, cropCanvas.width, cropCanvas.height
                    );
                    cropCtx.restore();

                    // 获取base64编码的图像数据
                    const base64 = cropCanvas.toDataURL('image/png').split(',')[1];

                    // 发送OCR请求
                    const url = 'http://127.0.0.1:1224/api/ocr';
                    const response = await fetch(url, {
                        method: 'POST',
                        body: JSON.stringify({ base64: base64 }),
                        headers: { 'Content-Type': 'application/json' }
                    });

                    if (!response.ok) {
                        throw new Error(`OCR服务返回错误: ${response.status}`);
                    }

                    const res = await response.json();

                    // 处理OCR响应
                    if (res.data && res.data.length > 0) {
                        // 处理识别结果...
                        statusIndicator.className = 'status-indicator green';
                        statusText.textContent = '识别成功';
                        statusText.style.color = '#34c759';

                        // 保存OCR结果
                        imgState.ocrTable = res.data;
                        imgState.step = 3;
                    } else {
                        throw new Error('OCR未返回有效数据');
                    }
                } catch (error) {
                    console.error('OCR识别失败:', error);
                    statusIndicator.className = 'status-indicator red';
                    statusText.textContent = '识别失败';
                    statusText.style.color = '#ff3b30';
                }

                // 更新按钮状态
                const btnResult = document.getElementById(`btn_result_${key}`);
                if (btnResult) {
                    btnResult.disabled = false;
                    btnResult.classList.remove('deactive');
                }
            }

            // 识别完成后状态指示和提示语
            statusIndicator.className = 'status-indicator ' + (success ? 'green' : 'red');
            var statusText = document.getElementById(`status_text_${key}`);
            if (!statusText) {
                statusText = document.createElement('span');
                statusText.id = `status_text_${key}`;
                statusText.style = 'margin-left:8px;font-size:16px;font-weight:600;';
                statusIndicator.parentNode.appendChild(statusText);
            }
            statusText.textContent = success ? '识别成功' : '识别失败';
            statusText.style.color = success ? '#34c759' : '#ff3b30';

            imgStates[key].ocrTable = ocrTable;
            imgStates[key].ocrText = ocrText;
            imgStates[key].step = 3;

            // 识别完成后，确保"显示结果"按钮可用且添加completed类
            const btnResult = document.getElementById(`btn_result_${key}`);
            const btnCrop = document.getElementById(`btn_crop_${key}`);
            if (btnResult) {
                btnResult.disabled = false;
                btnResult.classList.remove('deactive');
            }
            if (btnCrop) {
                btnCrop.classList.add('completed');
            }

            // 移除下方表格模块
            const tableWrap = document.getElementById(`tablewrap_${key}`);
            if (tableWrap) tableWrap.style.display = 'none';
            // Tab切换逻辑
            const tabSingle = document.getElementById('tab-single');
            const tabInter = document.getElementById('tab-inter');
            const tabContentSingle = document.getElementById('tab-content-single');
            const tabContentInter = document.getElementById('tab-content-inter');

            tabSingle.onclick = function () {
                tabSingle.classList.add('active');
                tabInter.classList.remove('active');
                tabContentSingle.style.display = '';
                tabContentInter.style.display = 'none';
            };

            tabInter.onclick = function () {
                tabInter.classList.add('active');
                tabSingle.classList.remove('active');
                tabContentInter.style.display = '';
                tabContentSingle.style.display = 'none';
            };

            // 初始化渲染
            renderImgGroups();

            // 表格弹窗展示逻辑
            if (!document.getElementById('ocr-modal')) {
                const modal = document.createElement('div');
                modal.id = 'ocr-modal';
                modal.style = 'display:none;position:fixed;z-index:9999;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.25);align-items:center;justify-content:center;';
                modal.innerHTML = `<div id="ocr-modal-content" style="background:#fff;padding:32px 36px 24px 36px;border-radius:16px;box-shadow:0 8px 32px 0 rgba(31,38,135,0.18);min-width:420px;max-width:96vw;max-height:90vh;overflow:auto;position:relative;">
                <button id="ocr-modal-close" style="position:absolute;right:18px;top:12px;font-size:22px;background:none;border:none;cursor:pointer;">×</button>
                <div id="ocr-modal-table"></div>
            </div>`;
                document.body.appendChild(modal);
                document.getElementById('ocr-modal-close').onclick = function () {
                    modal.style.display = 'none';
                };
            }

            // 事件委托：点击“查看结果”弹窗展示表格，并支持编辑和保存
            window.addEventListener('show-ocr-result', function (e) {
                const key = e.detail.key;
                const ocrTable = imgStates[key] && imgStates[key].ocrTable;
                const ocrText = imgStates[key] && imgStates[key].ocrText;
                const modal = document.getElementById('ocr-modal');
                const modalTable = document.getElementById('ocr-modal-table');
                if (ocrTable) {
                    modalTable.innerHTML = renderKJTable(ocrTable, true);
                    const form = document.getElementById('kj-edit-form');
                    form.onsubmit = function (ev) {
                        ev.preventDefault();
                        let newTable = [];
                        for (let i = 0; i < 3; i++) {
                            let row = [];
                            for (let j = 0; j < 5; j++) {
                                row.push(form[`cell_${i}_${j}`].value.trim());
                            }
                            newTable.push(row);
                        }
                        imgStates[key].ocrTable = newTable;
                        modalTable.innerHTML = renderKJTable(newTable, false);
                    };
                } else {
                    modalTable.innerHTML = `<pre style='font-size:16px;color:#888;'>${ocrText || '无内容'}</pre>`;
                }
                modal.style.display = 'flex';
            });

            // 解析文本为表格
            function parseTableFromText(text) {
                // 预处理：将常见的l/L误识别为1
                text = text.replace(/[lL]/g, '1');
                const lines = text.split(/\r?\n/).map(l => l.trim()).filter(l => l);
                const kjRows = [];
                for (let i = 0; i < lines.length; i++) {
                    const nums = lines[i].match(/-?\d*\.?\d+/g)?.slice(0, 5);
                    if (nums?.length >= 5) {
                        kjRows.push(nums);
                        if (kjRows.length >= 3) break;
                    }
                }


                if (kjRows.length === 3) return kjRows;

                return null;
            }

            // 渲染KJ表格（带边框、可编辑、带保存按钮）
            function renderKJTable(table, editable = true) {
                let html = `<form id="kj-edit-form"><table id="kj-edit-table" style="border-collapse:collapse;margin:0 auto;font-size:17px;box-shadow:0 2px 12px 0 rgba(0,122,255,0.10);">
                <thead><tr>
                    <th style="border:2px solid #007aff;background:#e3f0fc;"> </th>
                    <th style="border:2px solid #007aff;background:#e3f0fc;">Z</th>
                    <th style="border:2px solid #007aff;background:#e3f0fc;">X</th>
                    <th style="border:2px solid #007aff;background:#e3f0fc;">U</th>
                    <th style="border:2px solid #007aff;background:#e3f0fc;">R</th>
                    <th style="border:2px solid #007aff;background:#e3f0fc;">L</th>
                </tr></thead><tbody>`;
                const rowNames = ['KJ1', 'KJ2', 'KJ3'];
                for (let i = 0; i < 3; i++) {
                    html += `<tr><th style="border:2px solid #007aff;background:#e3f0fc;">${rowNames[i]}</th>`; for (let j = 0; j < 5; j++) {
                        if (editable) {
                            html += `<td style="border:2px solid #007aff;background:#fff;"><input type="text" name="cell_${i}_${j}" value="${table[i][j] || ''}" style="width:60px;text-align:center;font-size:17px;border:none;background:transparent;"></td>`;
                        } else {
                            html += `<td style="border:2px solid #007aff;background:#fff;font-weight:600;font-size:18px;">${table[i][j] || ''}</td>`;
                        }
                    }
                    html += '</tr>';
                }
                html += `</tbody></table>`;
                if (editable) {
                    html += `<div style="text-align:center;margin-top:18px;"><button type="submit" id="kj-save-btn" style="background:#007aff;color:#fff;font-size:17px;padding:8px 32px;border:none;border-radius:8px;cursor:pointer;">保存</button></div>`;
                }
                html += `</form>`;
                return html;
            }

            // 更新流程按钮状态
            function updateFlowButtons(currentGroup) {
                const prefix = currentGroup === 'img-group-gyzy-inter' ? 'inter_' : '';
                [{ key: 'gy_zy_max', label: '高压-中压 最大正' }, { key: 'gy_zy_min', label: '高压-中压 最小负' }].forEach(item => {
                    const key = prefix + item.key;
                    const btnSelect = document.getElementById(`btn_select_${key}`);
                    const btnCrop = document.getElementById(`btn_crop_${key}`);
                    const btnResult = document.getElementById(`btn_result_${key}`);
                    const step1 = document.getElementById(`step1_${key}`);
                    const step2 = document.getElementById(`step2_${key}`);
                    const step3 = document.getElementById(`step3_${key}`);

                    // 重置所有按钮的初始状态
                    btnSelect.disabled = false;
                    btnCrop.disabled = true;
                    btnResult.disabled = true;
                    btnCrop.classList.add('deactive');
                    btnResult.classList.add('deactive');

                    // 根据当前步骤更新按钮状态
                    if (imgStates[key]) {
                        if (imgStates[key].step >= 1) {
                            btnCrop.disabled = false;
                            btnCrop.classList.remove('deactive');
                            btnSelect.classList.add('completed');
                        }

                        if (imgStates[key].step >= 2) {
                            btnResult.disabled = false;
                            btnResult.classList.remove('deactive');
                            btnCrop.classList.add('completed');
                        }

                        if (imgStates[key].step >= 3) {
                            btnResult.classList.add('completed');
                        }
                    }
                });
            }

            // Tab切换时重置流程按钮状态
            tabSingle.onclick = function () {
                tabSingle.classList.add('active');
                tabInter.classList.remove('active');
                tabContentSingle.style.display = '';
                tabContentInter.style.display = 'none';
                updateFlowButtons('img-group-gyzy');
            };
            tabInter.onclick = function () {
                tabInter.classList.add('active');
                tabSingle.classList.remove('active');
                tabContentInter.style.display = '';
                tabContentSingle.style.display = 'none';
                updateFlowButtons('img-group-gyzy-inter');
            };

            // 初始化时仅第一个流程按钮可用
            updateFlowButtons('img-group-gyzy');

            // 添加 getOcrData 函数定义
            function getOcrData(states, key, colIndex, mapping) {
                const state = states[key];
                const result = {};

                if (state && state.ocrTable && state.ocrTable.length > 0) {
                    // 只取第一行（KJ1）的数据作为代表值
                    const row = state.ocrTable[0];
                    if (row && row.length > colIndex) {
                        const value = row[colIndex];
                        // 分别映射到 dd 和 dx 字段
                        if (mapping.dd) result[mapping.dd] = value;
                        if (mapping.dx) result[mapping.dx] = value;
                    }
                }
                return result;
            }

            // 导出功能
            document.getElementById('btn-export').addEventListener('click', async function () {
                // 获取表单数据
                const formData = new FormData(document.getElementById('info-form'));
                const templateData = {};
                // 基本信息
                templateData.company_name = formData.get('company_name') || '';
                templateData.operation_number = formData.get('operation_number') || '';
                templateData.device_model = formData.get('device_model') || '';
                templateData.manufacturer = formData.get('manufacturer') || '';
                // 收集电压等级信息
                templateData.ug = formData.get('voltage_high') + 'kV' || '';  // 高压
                templateData.uz = formData.get('voltage_medium') + 'kV' || '';  // 中压
                templateData.ud = formData.get('voltage_low') + 'kV' || '';  // 低压

                // 档位信息
                templateData.gd_zz = formData.get('voltage_high_max') || '';    // 高对中最正档位
                templateData.gd_ed = formData.get('voltage_high_rated') || '';  // 高对中额定档位
                templateData.gd_zf = formData.get('voltage_high_min') || '';    // 高对中最负档位
                templateData.zd_ed = formData.get('voltage_medium_rated') || '';  // 中对低额定档位

                templateData.serial_number = formData.get('serial_number') || '';
                templateData.production_date = formData.get('production_date') || '';
                templateData.test_date = formData.get('test_date') || '';
                templateData.test_nature = formData.get('test_nature') || '';
                templateData.test_leader = formData.get('test_leader') || '';
                templateData.test_people = formData.get('test_people') || '';
                templateData.weather = formData.get('weather') || '';
                templateData.conclusion = formData.get('conclusion') || '';
                templateData.temperature = formData.get('temperature') || '';
                templateData.humidity = formData.get('humidity') || '';

                // 铭牌参数
                templateData.mp_gdz_zz = formData.get('np_gy_zy_max') || '';    // 高对中最大正
                templateData.mp_gdz_ed = formData.get('np_gy_zy_rated') || '';  // 高对中额定
                templateData.mp_gdz_zf = formData.get('np_gy_zy_min') || '';    // 高对中最大负
                templateData.mp_gdd_zz = formData.get('np_gy_dy_max') || '';    // 高对低最大正
                templateData.mp_gdd_ed = formData.get('np_gy_dy_rated') || '';  // 高对低额定
                templateData.mp_gdd_zf = formData.get('np_gy_dy_min') || '';    // 高对低最大负
                templateData.mp_zdd_ed = formData.get('np_zy_dy_rated') || '';  // 中对低额定

                // 收集测试数据，修正列索引映射
                const testData = {};

                // === 处理额定数据 ===
                // 高中压额定数据
                Object.assign(testData,
                    getOcrData(imgStates, 'gy_zy_rated', 0, { dd: 'zked_gdzd', dx: 'zked_gdzx' }),  // Z列 -> zk短路阻抗
                    getOcrData(imgStates, 'gy_zy_rated', 1, { dd: 'xked_gdzd', dx: 'xked_gdzx' }),  // X列 -> xk短路电抗
                    getOcrData(imgStates, 'gy_zy_rated', 2, { dd: 'uked_gdzd', dx: 'uked_gdzx' }),  // U列 -> uk阻抗电压
                    getOcrData(imgStates, 'gy_zy_rated', 4, { dd: 'lked_gdzd', dx: 'lked_gdzx' })   // L列 -> lk漏电感
                );

                // 中低压额定数据
                Object.assign(testData,
                    getOcrData(imgStates, 'zy_dy_rated', 0, { dd: 'zked_zddd', dx: 'zked_zddx' }),  // Z列 -> zk短路阻抗
                    getOcrData(imgStates, 'zy_dy_rated', 1, { dd: 'xked_zddd', dx: 'xked_zddx' }),  // X列 -> xk短路电抗
                    getOcrData(imgStates, 'zy_dy_rated', 2, { dd: 'uked_zddd', dx: 'uked_zddx' }),  // U列 -> uk阻抗电压
                    getOcrData(imgStates, 'zy_dy_rated', 4, { dd: 'lked_zddd', dx: 'lked_zddx' })   // L列 -> lk漏电感
                );

                // === 处理最大正状态数据 ===
                // 高中压最大正数据
                Object.assign(testData,
                    getOcrData(imgStates, 'gy_zy_max', 0, { dd: 'zkzz_gdzd', dx: 'zkzz_gdzx' }),    // Z列 -> zk短路阻抗
                    getOcrData(imgStates, 'gy_zy_max', 1, { dd: 'xkzz_gdzd', dx: 'xkzz_gdzx' }),    // X列 -> xk短路电抗
                    getOcrData(imgStates, 'gy_zy_max', 2, { dd: 'ukzz_gdzd', dx: 'ukzz_gdzx' }),    // U列 -> uk阻抗电压
                    getOcrData(imgStates, 'gy_zy_max', 4, { dd: 'lkzz_gdzd', dx: 'lkzz_gdzx' })     // L列 -> lk漏电感
                );

                // === 处理最大负状态数据 ===
                // 高中压最大负数据
                Object.assign(testData,
                    getOcrData(imgStates, 'gy_zy_min', 0, { dd: 'zkzf_gdzd', dx: 'zkzf_gdzx' }),    // Z列 -> zk短路阻抗
                    getOcrData(imgStates, 'gy_zy_min', 1, { dd: 'xkzf_gdzd', dx: 'xkzf_gdzx' }),    // X列 -> xk短路电抗
                    getOcrData(imgStates, 'gy_zy_min', 2, { dd: 'ukzf_gdzd', dx: 'ukzf_gdzx' }),    // U列 -> uk阻抗电压
                    getOcrData(imgStates, 'gy_zy_min', 4, { dd: 'lkzf_gdzd', dx: 'lkzf_gdzx' })     // L列 -> lk漏电感
                );

                // === 自动批量映射OCR表格到所有占位符 ===
                // 单相法tab
                const ocrConfig = [
                    { key: 'gy_zy_max', prefix: 'zz_gdzd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'gy_zy_rated', prefix: 'ed_gdzd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'gy_zy_min', prefix: 'zf_gdzd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'gy_dy_max', prefix: 'zz_gddd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'gy_dy_rated', prefix: 'ed_gddd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'gy_dy_min', prefix: 'zf_gddd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'zy_dy_rated', prefix: 'ed_zddd', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    // 相间法tab
                    { key: 'inter_gy_zy_max', prefix: 'zz_gdzx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'inter_gy_zy_rated', prefix: 'ed_gdzx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'inter_gy_zy_min', prefix: 'zf_gdzx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'inter_gy_dy_max', prefix: 'zz_gddx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'inter_gy_dy_rated', prefix: 'ed_gddx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'inter_gy_dy_min', prefix: 'zf_gddx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                    { key: 'inter_zy_dy_rated', prefix: 'ed_zddx', colMap: { zk: 0, xk: 1, uk: 2, lk: 4 } },
                ];
                const typeMap = { zk: 'zk', xk: 'xk', uk: 'uk', lk: 'lk' };
                const phaseMap = ['a', 'b', 'c']; // KJ1、KJ2、KJ3
                ocrConfig.forEach(cfg => {
                    const table = imgStates[cfg.key]?.ocrTable;
                    if (Array.isArray(table) && table.length >= 3) {
                        Object.entries(cfg.colMap).forEach(([type, colIdx]) => {
                            for (let i = 0; i < 3; i++) {
                                const val = table[i][colIdx] || '';
                                const field = `${typeMap[type]}${cfg.prefix}_${phaseMap[i]}`;
                                templateData[field] = val;
                            }
                        });
                    }
                }
                );

                // 合并所有数据
                Object.assign(templateData, testData);

                // === 自动计算各类数据abc三相平均值 ===
                const avgConfig = [
                    // 高对低 单相法
                    { prefix: 'zz_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 高对低 相间法
                    { prefix: 'zz_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 高对中 单相法
                    { prefix: 'zz_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 高对中 相间法
                    { prefix: 'zz_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 中对低 单相法
                    { prefix: 'ed_zddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 中对低 相间法
                    { prefix: 'ed_zddx', types: ['uk', 'zk', 'xk', 'lk'] }
                ];
                avgConfig.forEach(cfg => {
                    cfg.types.forEach(type => {
                        const a = parseFloat(templateData[`${type}${cfg.prefix}_a`]) || 0;
                        const b = parseFloat(templateData[`${type}${cfg.prefix}_b`]) || 0;
                        const c = parseFloat(templateData[`${type}${cfg.prefix}_c`]) || 0;
                        // 只要有一个非空就算平均
                        if (
                            templateData[`${type}${cfg.prefix}_a`] !== undefined ||
                            templateData[`${type}${cfg.prefix}_b`] !== undefined ||
                            templateData[`${type}${cfg.prefix}_c`] !== undefined
                        ) {
                            const avg = ((a + b + c) / 3).toFixed(3);
                            templateData[`${type}${cfg.prefix}_avg`] = avg;
                        }
                    });
                });

                // === 计算横比 (最大值-最小值)/平均值 ===
                const hbConfig = [
                    // 高对低 单相法
                    { prefix: 'zz_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 高对低 相间法
                    { prefix: 'zz_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 高对中 单相法
                    { prefix: 'zz_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 高对中 相间法
                    { prefix: 'zz_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'ed_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                    { prefix: 'zf_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 中对低 单相法
                    { prefix: 'ed_zddd', types: ['uk', 'zk', 'xk', 'lk'] },
                    // 中对低 相间法
                    { prefix: 'ed_zddx', types: ['uk', 'zk', 'xk', 'lk'] },
                ];
                hbConfig.forEach(cfg => {
                    cfg.types.forEach(type => {
                        const a = parseFloat(templateData[`${type}${cfg.prefix}_a`]) || 0;
                        const b = parseFloat(templateData[`${type}${cfg.prefix}_b`]) || 0;
                        const c = parseFloat(templateData[`${type}${cfg.prefix}_c`]) || 0;
                        const avg = parseFloat(templateData[`${type}${cfg.prefix}_avg`]) || 0;

                        if (avg > 0) {
                            const values = [a, b, c].filter(v => !isNaN(v));
                            if (values.length > 0) {
                                const max = Math.max(...values);
                                const min = Math.min(...values);
                                const hb = ((max - min) / avg * 100).toFixed(2) + '%';
                                templateData[`${type}${cfg.prefix}_hb`] = hb;
                            }
                        }
                    });
                });

                // === 计算相间法纵比 (实测阻抗-铭牌阻抗)/铭牌阻抗 ===
                const zbConfig = [
                    // 高对低 相间法
                    { prefix: 'zz_gddx', nameplateField: 'mp_gdd_zz' },
                    { prefix: 'ed_gddx', nameplateField: 'mp_gdd_ed' },
                    { prefix: 'zf_gddx', nameplateField: 'mp_gdd_zf' },
                    // 高对中 相间法
                    { prefix: 'zz_gdzx', nameplateField: 'mp_gdz_zz' },
                    { prefix: 'ed_gdzx', nameplateField: 'mp_gdz_ed' },
                    { prefix: 'zf_gdzx', nameplateField: 'mp_gdz_zf' },
                    // 中对低 相间法
                    { prefix: 'ed_zddx', nameplateField: 'mp_zdd_ed' }
                ];
                zbConfig.forEach(cfg => {
                    const measuredValue = parseFloat(templateData[`uk${cfg.prefix}_avg`]) || 0;
                    const nameplateValue = parseFloat(templateData[cfg.nameplateField]) || 0;

                    if (nameplateValue > 0 && measuredValue > 0) {
                        const zb = ((measuredValue - nameplateValue) / nameplateValue * 100).toFixed(2) + '%';
                        templateData[`uk${cfg.prefix}_zb`] = zb;
                    }
                });

                // 过滤未识别的绕组对数据
                ocrConfig.forEach(cfg => {
                    const state = imgStates[cfg.key];
                    // 检查是否未识别（step < 3）
                    if (!state || state.step < 3) {
                        // 生成所有相关字段并删除
                        Object.keys(cfg.colMap).forEach(type => {
                            const typeKey = typeMap[type];
                            phaseMap.forEach(phase => {
                                const field = `${typeKey}${cfg.prefix}_${phase}`;
                                delete templateData[field];
                                // 删除横比和纵比字段
                                delete templateData[`${typeKey}${cfg.prefix}_hb`];
                                delete templateData[`${typeKey}${cfg.prefix}_zb`];
                                delete templateData[`${typeKey}${cfg.prefix}_avg`];
                            });
                        });
                    }
                });

                console.log('导出前的 templateData:', templateData); // 调试用，检查数据内容
                /* 暂时屏蔽图片数据合并 */
                /* // 合并图片数据到模板数据
                if (window.templateData && window.templateData.nameplate_img) {
                    templateData.nameplate_img = window.templateData.nameplate_img;
                } */
                generateDocumentWithTemplate(templateData);

            });
            const form = document.getElementById('info-form');
            const formData = new FormData(form);
            console.log('conclusion select value:', document.getElementById('conclusion').value);
            console.log('formData.get(conclusion):', formData.get('conclusion'));
            console.log('imgStates:', imgStates);
            ocrConfig.forEach(cfg => {
                const table = imgStates[cfg.key]?.ocrTable;
                console.log(cfg.key, table);
            });
            console.log(templateData);
            // === 自动计算各类数据abc三相平均值 ===
            const avgConfig = [
                // 高对低 单相法
                { prefix: 'zz_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'ed_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'zf_gddd', types: ['uk', 'zk', 'xk', 'lk'] },
                // 高对低 相间法
                { prefix: 'zz_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'ed_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'zf_gddx', types: ['uk', 'zk', 'xk', 'lk'] },
                // 高对中 单相法
                { prefix: 'zz_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'ed_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'zf_gdzd', types: ['uk', 'zk', 'xk', 'lk'] },
                // 高对中 相间法
                { prefix: 'zz_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'ed_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                { prefix: 'zf_gdzx', types: ['uk', 'zk', 'xk', 'lk'] },
                // 中对低 单相法
                { prefix: 'ed_zddd', types: ['uk', 'zk', 'xk', 'lk'] },
                // 中对低 相间法
                { prefix: 'ed_zddx', types: ['uk', 'zk', 'xk', 'lk'] },
            ];
            avgConfig.forEach(cfg => {
                cfg.types.forEach(type => {
                    const a = parseFloat(templateData[`${type}${cfg.prefix}_a`]) || 0;
                    const b = parseFloat(templateData[`${type}${cfg.prefix}_b`]) || 0;
                    const c = parseFloat(templateData[`${type}${cfg.prefix}_c`]) || 0;
                    // 只要有一个非空就算平均
                    if (
                        templateData[`${type}${cfg.prefix}_a`] !== undefined ||
                        templateData[`${type}${cfg.prefix}_b`] !== undefined ||
                        templateData[`${type}${cfg.prefix}_c`] !== undefined
                    ) {
                        const avg = ((a + b + c) / 3).toFixed(3);
                        templateData[`${type}${cfg.prefix}_avg`] = avg;
                    }
                });
            });

            // 生成文档
            function generateDocument(templateData) {
                return new Promise((resolve, reject) => {
                    // 创建文件选择输入框
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.accept = '.docx';
                    fileInput.style.position = 'fixed';
                    fileInput.style.top = '-100px';  // 将元素移出可视区域而不是隐藏它
                    document.body.appendChild(fileInput);

                    // 清理函数
                    const cleanup = () => {
                        if (document.body.contains(fileInput)) {
                            document.body.removeChild(fileInput);
                        }
                    };

                    // 处理文件选择
                    fileInput.onchange = function (e) {
                        const templateFile = e.target.files[0];
                        if (!templateFile) {
                            cleanup();
                            reject(new Error('未选择模板文件'));
                            return;
                        }

                        // 读取选中的模板文件
                        const reader = new FileReader();
                        reader.onload = function (e) {
                            try {
                                const content = e.target.result;
                                const zip = new PizZip(content);
                                const doc = new window.docxtemplater(zip, {
                                    /* 暂时屏蔽图片模块 */
                                    // modules: [imageModule],
                                    paragraphLoop: true,
                                    linebreaks: true
                                });

                                // 设置模板变量
                                doc.setData(templateData);

                                // 渲染文档
                                doc.render();

                                // 输出文档
                                const out = doc.getZip().generate({
                                    type: "blob",
                                    mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                                });

                                // 使用当前日期作为文件名
                                const today = new Date();
                                const fileName = `测试报告_${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}.docx`;

                                saveAs(out, fileName);
                                cleanup();
                                resolve();
                            } catch (error) {
                                console.error('文档生成错误:', error);
                                cleanup();
                                reject(error);
                            }
                        };

                        reader.onerror = function (error) {
                            console.error('读取模板文件错误:', error);
                            cleanup();
                            reject(error);
                        };

                        reader.readAsArrayBuffer(templateFile);
                    };


                    // 当点击取消或关闭文件选择对话框时
                    fileInput.onabort = function () {
                        cleanup();
                        reject(new Error('用户取消了操作'));
                    };

                    // 使用setTimeout来确保文件选择对话框能正确显示
                    setTimeout(() => {
                        fileInput.click();
                    }, 100);
                });
            }

            // 处理模板选择变化
            function handleTemplateSelection(value) {
                const customTemplateSection = document.getElementById('custom-template-section');
                customTemplateSection.style.display = value === 'custom' ? 'block' : 'none';
            } async function generateDocumentWithTemplate(templateData) {
                try {
                    // 使用自定义模板
                    const customTemplateInput = document.getElementById('custom-template');
                    if (!customTemplateInput.files[0]) {
                        throw new Error('请选择Word模板文件');
                    }
                    const reader = new FileReader();
                    reader.onload = async (e) => {
                        await processTemplate(e.target.result, templateData);
                    };
                    reader.readAsArrayBuffer(customTemplateInput.files[0]);
                } catch (error) {
                    console.error('文档生成错误:', error);
                    alert('生成文档时出错：' + error.message);
                }
            } async function processTemplate(templateContent, templateData) {
                try {
                    const zip = new PizZip(templateContent);

                    /* 暂时屏蔽ImageModule配置 */
                    /* // 配置 ImageModule
                    const imageModule = new ImageModule({
                        centered: true,
                        fileType: "docx",
                        getImage: function (tagValue) {
                            // 从base64字符串提取并转换图片数据
                            if (!tagValue) return null;
                            const base64Data = tagValue.replace(/^data:image\/(png|jpg|jpeg);base64,/, '');
                            return Uint8Array.from(atob(base64Data), c => c.charCodeAt(0));
                        },
                        getSize: function (imgBuffer) {
                            // 返回图片尺寸 (宽度, 高度) in pixels
                            return [400, 300]; // 可根据实际需求调整
                            if (!tagValue || typeof tagValue !== 'string') return null;

                            // 从 base64 转换为 ArrayBuffer
                            const base64Match = tagValue.match(/^data:image\/(png|jpg|jpeg|gif);base64,(.+)$/);
                            if (!base64Match) return null;

                            const binaryString = window.atob(base64Match[2]);
                            const bytes = new Uint8Array(binaryString.length);
                            for (let i = 0; i < binaryString.length; i++) {
                                bytes[i] = binaryString.charCodeAt(i);
                            }
                            return bytes.buffer;
                        },
                        getSize: function () {
                            // 返回图片大小，单位是 EMU (English Metric Unit)
                            return [6000000, 4000000];  // 约 15cm x 10cm
                        }
                    }); */

                    // 创建文档实例
                    const doc = new window.docxtemplater(zip, {
                        paragraphLoop: true,
                        linebreaks: true,
                        //   modules: [imageModule]
                    });
                    // 直接使用原始数据，不需要再次映射
                    const mappedData = templateData;

                    // 设置模板数据
                    doc.setData(mappedData);

                    // 渲染文档
                    doc.render();

                    // 生成输出
                    const out = doc.getZip().generate({
                        type: "blob",
                        mimeType: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    });

                    // 使用变电站名称和日期作为文件名
                    const today = new Date();
                    const fileName = `测试报告_${mappedData.company_name}_${mappedData.operation_number}_${today.getFullYear()}${(today.getMonth() + 1).toString().padStart(2, '0')}${today.getDate().toString().padStart(2, '0')}.docx`;

                    // 保存文件
                    saveAs(out, fileName);
                } catch (error) {
                    console.error('处理模板时出错:', error);
                    throw new Error('处理模板时出错: ' + error.message);
                }
            }

            function validateVoltagePositions() {
                const highMax = parseInt(document.getElementById('voltage_high_max').value);
                const highRated = parseInt(document.getElementById('voltage_high_rated').value);
                const highMin = parseInt(document.getElementById('voltage_high_min').value);

                // 验证高压侧档位
                if (highMax >= highRated || highRated >= highMin) {
                    alert('高压侧档位必须满足：最正档位 < 额定档位 < 最负档位');
                    return false;
                }

                return true;
            }

            // 添加档位输入验证
            document.getElementById('voltage_high_max').addEventListener('change', validateVoltagePositions);
            document.getElementById('voltage_high_rated').addEventListener('change', validateVoltagePositions);
            document.getElementById('voltage_high_min').addEventListener('change', validateVoltagePositions);

            function updateVoltages() {
                const highVoltageSelect = document.getElementById('voltage_high');
                const mediumVoltageSelect = document.getElementById('voltage_medium');
                const lowVoltageSelect = document.getElementById('voltage_low');

                const highVoltage = highVoltageSelect.value;

                // 重置disabled状态
                mediumVoltageSelect.disabled = false;

                // 根据高压值设置中压和低压的可选项和默认值
                switch (highVoltage) {
                    case '500':
                        // 清空并添加新的选项
                        mediumVoltageSelect.innerHTML = `
                        <option value="220">220kV</option>
                        <option value="110">110kV</option>
                        <option value="35">35kV</option>
                    `;
                        lowVoltageSelect.innerHTML = `
                        <option value="35">35kV</option>
                        <option value="10">10kV</option>
                    `;
                        // 设置默认值
                        mediumVoltageSelect.value = '220';
                        lowVoltageSelect.value = '35';
                        break;

                    case '220':
                        mediumVoltageSelect.innerHTML = `
                        <option value="110">110kV</option>
                        <option value="35">35kV</option>
                    `;
                        lowVoltageSelect.innerHTML = `
                        <option value="35">35kV</option>
                        <option value="10">10kV</option>
                    `;
                        mediumVoltageSelect.value = '110';
                        lowVoltageSelect.value = '35';
                        break;

                    case '110':
                        mediumVoltageSelect.innerHTML = `
                        <option value="35">35kV</option>
                        <option value="none">/</option>
                    `;
                        lowVoltageSelect.innerHTML = `
                        <option value="10">10kV</option>
                        <option value="35">35kV</option>
                    `;
                        mediumVoltageSelect.value = '35';
                        lowVoltageSelect.value = '10';
                        break;

                    case '35':
                        mediumVoltageSelect.innerHTML = `
                        <option value="none">/</option>
                    `;
                        lowVoltageSelect.innerHTML = `
                        <option value="10">10kV</option>
                    `;
                        mediumVoltageSelect.value = 'none';
                        lowVoltageSelect.value = '10';
                        mediumVoltageSelect.disabled = true;
                        break;
                }

                // 当选择35kV作为高压时，禁用中压档位输入
                const mediumRatedInput = document.getElementById('voltage_medium_rated');
                if (highVoltage === '35') {
                    mediumRatedInput.disabled = true;
                    mediumRatedInput.value = '';
                } else {
                    mediumRatedInput.disabled = false;
                }
            }

            // 页面加载时初始化电压值
            document.addEventListener('DOMContentLoaded', function () {
                updateVoltages();
            });
        </script>
</body>

</html>